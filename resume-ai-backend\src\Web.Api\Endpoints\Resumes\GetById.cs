using Application.Abstractions.Messaging;
using Application.Resumes.GetById;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Resumes;

internal sealed class GetById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("resumes/{id:guid}", GetResumeAsync)
            .WithTags(Tags.Resumes)
            .WithName("GetResume")
            .Produces<ResumeResponse>()
            .ProducesValidationProblem()
            .RequireAuthorization();
    }

    private static async Task<IResult> GetResumeAsync(
        Guid id,
        IQueryHandler<GetResumeByIdQuery, ResumeResponse> handler,
        CancellationToken cancellationToken,
        bool includeContent = true)
    {
        var query = new GetResumeByIdQuery(id, includeContent);

        Result<ResumeResponse> result = await handler.<PERSON><PERSON>(query, cancellationToken);

        return result.Match(Results.Ok, CustomResults.Problem);
    }
}
