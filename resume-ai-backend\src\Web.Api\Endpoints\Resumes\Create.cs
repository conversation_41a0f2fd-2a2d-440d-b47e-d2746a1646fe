using Application.Abstractions.Messaging;
using Application.Resumes.Create;
using Application.Resumes.GetById;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Resumes;

internal sealed class CreateResume : IEndpoint
{
    public sealed record Request(
        Guid UserId,
        Guid? ParentId,
        string ResumeContent
    );

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("resumes", CreateResumeAsync)
            .WithTags(Tags.Resumes)
            .WithName("CreateResume")
            .Produces<Guid>()
            .ProducesValidationProblem()
            .RequireAuthorization();
    }

    private static async Task<IResult> CreateResumeAsync(
        Request request,
        ICommandHandler<CreateResumeCommand, Guid> handler,
        CancellationToken cancellationToken)
    {
        var command = new CreateResumeCommand
        {
            UserId = request.UserId,
            ParentId = request.ParentId,
            ResumeContent = request.ResumeContent
        };

        Result<Guid> result = await handler.Handle(command, cancellationToken);

        return result.Match(
            resumeId => Results.Created($"/resumes/{resumeId}", resumeId),
            CustomResults.Problem);
    }
}
