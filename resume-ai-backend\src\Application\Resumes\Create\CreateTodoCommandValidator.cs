﻿using FluentValidation;
using SharedKernel;

namespace Application.Resumes.Create;

public class CreateResumeCommandValidator : AbstractValidator<CreateResumeCommand>
{
    private const int MaxContentLength = 1_000_000; // 1MB limit for HTML content
    private const int MinContentLength = 10; // Minimum content length

    public CreateResumeCommandValidator()
    {
        RuleFor(c => c.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(c => c.ResumeContent)
            .NotEmpty()
            .WithMessage("Resume content is required")
            .Length(MinContentLength, MaxContentLength)
            .WithMessage($"Resume content must be between {MinContentLength} and {MaxContentLength:N0} characters")
            .Must(BeValidHtmlContent)
            .WithMessage("Resume content must be valid HTML");

        RuleFor(c => c.ParentId)
            .NotEqual(Guid.Empty)
            .When(c => c.ParentId.HasValue)
            .WithMessage("Parent ID cannot be empty GUID");
    }

    private static bool BeValidHtmlContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            return false;
        }

        // Basic HTML validation - check for basic HTML structure
        string trimmedContent = content.Trim();

        // Allow both full HTML documents and HTML fragments
        return trimmedContent.Contains('<') && trimmedContent.Contains('>');
    }
}
